<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AuditLuma安全审计报告 - {{ project_name }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <style>
        :root {
            /* 主色调 */
            --primary-color: #00c0f5;
            --primary-dark: #0078d7;
            --primary-light: #b6e3ff;
            
            /* 辅助色 */
            --secondary-color: #586f7c;
            --background-color: #f8fbff;
            --card-bg: #ffffff;
            
            /* 危险等级颜色 */
            --critical-color: #ff3a3a;
            --high-color: #ff6b6b;
            --medium-color: #ffbd59;
            --low-color: #7ad84e;
            --info-color: #00c0f5;
            
            /* 文字颜色 */
            --text-primary: #2b3a4d;
            --text-secondary: #586f7c;
            --text-light: #f8f9fa;
            
            /* 其他 */
            --border-color: #e5e9ef;
            --shadow: 0 2px 8px rgba(0,120,215,0.08);
            --hover-shadow: 0 4px 12px rgba(0,120,215,0.15);
            --card-radius: 8px;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0;
            background-color: transparent;
        }
        
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 30px;
            border-radius: 0 0 var(--card-radius) var(--card-radius);
            margin-bottom: 30px;
            box-shadow: var(--shadow);
        }
        
        .dashboard {
            display: flex;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .sidebar {
            width: 280px;
            position: sticky;
            top: 20px;
            align-self: flex-start;
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            padding: 20px;
            box-shadow: var(--shadow);
            margin-right: 20px;
            margin-bottom: 20px;
        }
        
        .main-content {
            flex: 1;
            min-width: 0;
        }
        
        .card {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            box-shadow: var(--shadow);
            margin-bottom: 20px;
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .card:hover {
            box-shadow: var(--hover-shadow);
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0,0,0,0.02);
        }
        
        .card-body {
            padding: 20px;
        }
        
        h1, h2, h3, h4 {
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        h1 {
            font-size: 2em;
            margin: 0;
            color: white;
        }
        
        h2 {
            font-size: 1.5em;
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        h3 {
            font-size: 1.2em;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }
        
        .stat-card .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: var(--primary-color);
        }
        
        .stat-card .count {
            font-size: 2em;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 5px;
        }
        
        .stat-card .label {
            color: var(--text-secondary);
            font-size: 0.9em;
        }
        
        .severity-critical .count { color: var(--critical-color); }
        .severity-high .count { color: var(--high-color); }
        .severity-medium .count { color: var(--medium-color); }
        .severity-low .count { color: var(--low-color); }
        .severity-info .count { color: var(--info-color); }
        
        .severity-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            font-size: 0.8em;
            text-transform: uppercase;
        }
        
        .badge-critical { background-color: var(--critical-color); }
        .badge-high { background-color: var(--high-color); }
        .badge-medium { background-color: var(--medium-color); color: #333; }
        .badge-low { background-color: var(--low-color); }
        .badge-info { background-color: var(--info-color); }
        
        /* 导航菜单 */
        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0 0 20px 0;
        }
        
        .nav-menu li {
            margin-bottom: 5px;
        }
        
        .nav-menu a {
            display: block;
            padding: 10px 15px;
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background-color: var(--primary-light);
            color: var(--primary-dark);
        }
        
        .nav-menu a i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }
        
        /* 过滤器 */
        .filters {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }
        
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-group:last-child {
            margin-bottom: 0;
        }
        
        .filter-group h4 {
            font-size: 0.9em;
            margin-bottom: 10px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-checkbox {
            display: none;
        }
        
        .filter-label {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #f0f0f0;
            color: var(--text-secondary);
        }
        
        .filter-checkbox:checked + .filter-label {
            background-color: var(--primary-color);
            color: white;
        }
        
        .filter-label-critical { border: 1px solid var(--critical-color); }
        .filter-label-high { border: 1px solid var(--high-color); }
        .filter-label-medium { border: 1px solid var(--medium-color); }
        .filter-label-low { border: 1px solid var(--low-color); }
        .filter-label-info { border: 1px solid var(--info-color); }
        
        .filter-checkbox:checked + .filter-label-critical { background-color: var(--critical-color); }
        .filter-checkbox:checked + .filter-label-high { background-color: var(--high-color); }
        .filter-checkbox:checked + .filter-label-medium { background-color: var(--medium-color); color: #333; }
        .filter-checkbox:checked + .filter-label-low { background-color: var(--low-color); }
        .filter-checkbox:checked + .filter-label-info { background-color: var(--info-color); }
        
        /* 搜索框 */
        .search-box {
            position: relative;
            margin-bottom: 15px;
        }
        
        .search-box input {
            width: 100%;
            padding: 10px 15px 10px 35px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 0.9em;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.15);
        }
        
        .search-box i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
        }
        
        /* 代码部分 - 已更新以支持高亮 */
        .code-section {
            background-color: transparent;
            margin: 10px 0;
            overflow-x: auto;
            position: relative;
        }
        
        .code-header {
            background-color: #1a1c25;
            padding: 8px 15px;
            border-radius: 5px 5px 0 0;
            color: #d0d0d0;
            font-size: 0.9em;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .code-path {
            opacity: 0.7;
        }
        
        /* 添加Markdown安全显示样式 - 更新以支持高亮 */
        .markdown-display {
            background-color: var(--card-bg);
            border-radius: 4px;
            border: 1px solid var(--border-color);
            position: relative;
        }
        
        .markdown-display pre {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            margin: 0;
            padding: 0;
            overflow-x: auto;
            max-height: 400px;
            background-color: transparent;
            border-radius: 4px;
        }
        
        .markdown-display code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            background-color: #f5f5f5;
            color: var(--text-primary);
            border-radius: 3px;
            padding: 0.1em 0.3em;
        }
        
        .markdown-display blockquote {
            border-left: 3px solid var(--primary-color);
            margin: 1rem 0;
            padding: 0.5rem 0 0.5rem 1rem;
            color: var(--text-secondary);
        }
        
        /* 高亮代码块样式 */
        .markdown-display .hljs {
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
        }
        
        /* 一键复制样式 */
        .code-copy {
            background: none;
            border: none;
            color: #a0a0a0;
            cursor: pointer;
            transition: color 0.2s ease;
            z-index: 10;
        }
        
        .code-copy:hover {
            color: white;
        }
        
        /* 确保代码区域可滚动但不溢出 */
        .code-section {
            max-width: 100%;
            overflow-x: auto;
        }
        
        .code-section pre {
            overflow-x: auto;
            word-wrap: normal;
        }
        
        /* Markdown中语法高亮代码块 */
        .markdown-content {
            padding: 1rem;
        }
        
        .markdown-content p {
            margin-bottom: 1rem;
        }
        
        .markdown-content h1, 
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4 {
            margin-top: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .markdown-content ul,
        .markdown-content ol {
            margin-left: 2rem;
            margin-bottom: 1rem;
        }
        
        .markdown-content code {
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            padding: 0.2em 0.4em;
            background-color: rgba(0,0,0,0.05);
            border-radius: 3px;
        }
        
        /* 图表容器 */
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            box-shadow: var(--shadow);
            padding: 20px;
            height: 300px;
        }
        
        /* 漏洞卡片 */
        .vuln-card {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            box-shadow: var(--shadow);
            margin-bottom: 15px;
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .vuln-card .card-body {
            padding: 20px;
        }
        
        .vuln-card .card-footer {
            background-color: rgba(0,0,0,0.02);
            border-top: 1px solid var(--border-color);
            padding: 15px 20px;
        }
        
        /* 折叠状态 */
        .vuln-card:not(.expanded) .card-body,
        .vuln-card:not(.expanded) .card-footer {
            display: none;
        }
        
        .vuln-card.expanded {
            box-shadow: var(--hover-shadow);
        }
        
        .vuln-card .card-header {
            cursor: pointer;
            position: relative;
            padding-right: 40px;
        }
        
        .vuln-card .card-header:after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.2s ease;
        }
        
        .vuln-card.expanded .card-header:after {
            transform: translateY(-50%) rotate(180deg);
        }
        
        /* 页脚 */
        footer {
            text-align: center;
            padding: 30px 20px;
            margin-top: 50px;
            color: var(--text-secondary);
            font-size: 0.9em;
            border-top: 1px solid var(--border-color);
            background-color: var(--card-bg);
        }
        
        footer a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        /* 推荐建议网格 */
        .recommendations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .recommendation-item {
            background-color: var(--card-bg);
            border-radius: var(--card-radius);
            border: 1px solid var(--border-color);
            padding: 20px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .recommendation-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--hover-shadow);
        }
        
        .recommendation-item .icon {
            font-size: 2em;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .recommendation-item h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-dark);
        }
        
        .recommendation-item p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.9em;
        }
        
        /* 时间线 */
        .timeline {
            position: relative;
            margin: 20px 0;
            padding-left: 20px;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: var(--primary-light);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-item:last-child {
            margin-bottom: 0;
        }
        
        .timeline-marker {
            position: absolute;
            left: -24px;
            top: 5px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 2px solid var(--primary-light);
        }
        
        .timeline-content {
            padding-left: 15px;
        }
        
        .timeline-content h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--primary-dark);
        }
        
        .timeline-content p {
            margin-bottom: 10px;
        }
        
        /* 按钮 */
        .btn {
            display: inline-block;
            padding: 6px 12px;
            background-color: var(--primary-light);
            color: var(--primary-dark);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.2s ease;
        }
        
        .btn:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn i {
            margin-right: 5px;
        }
        
        /* 计数徽章 */
        .count-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 10px;
            background-color: var(--primary-light);
            color: var(--primary-dark);
            font-size: 0.8em;
            margin-left: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .dashboard {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                position: static;
                margin-right: 0;
                margin-bottom: 20px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            }
            
            .recommendations-grid {
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            header {
                padding: 20px;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .stat-card .count {
                font-size: 1.5em;
            }
            
            .recommendations-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-shield-alt"></i> AuditLuma安全审计报告</h1>
            <p>项目名称: {{ project_name }} | 扫描日期: {{ scan_date }}</p>
        </header>
        
        <div class="dashboard">
            <!-- 侧边栏导航和过滤器 -->
            <div class="sidebar">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="搜索漏洞..." />
                </div>
                
                <ul class="nav-menu">
                    <li><a href="#overview" class="active"><i class="fas fa-home"></i> 概览</a></li>
                    <li><a href="#vulnerabilities"><i class="fas fa-bug"></i> 漏洞详情</a></li>
                    <li><a href="#dependency"><i class="fas fa-project-diagram"></i> 依赖关系</a></li>
                    <li><a href="#recommendations"><i class="fas fa-lightbulb"></i> 安全建议</a></li>
                    <li><a href="#statistics"><i class="fas fa-chart-pie"></i> 统计数据</a></li>
                </ul>
                
                <div class="filters">
                    <h3>漏洞过滤器</h3>
                    
                    <div class="filter-group">
                        <h4>严重程度</h4>
                        <div class="filter-options">
                            <input type="checkbox" id="filter-critical" class="filter-checkbox" checked data-severity="critical">
                            <label for="filter-critical" class="filter-label filter-label-critical">严重</label>
                            
                            <input type="checkbox" id="filter-high" class="filter-checkbox" checked data-severity="high">
                            <label for="filter-high" class="filter-label filter-label-high">高危</label>
                            
                            <input type="checkbox" id="filter-medium" class="filter-checkbox" checked data-severity="medium">
                            <label for="filter-medium" class="filter-label filter-label-medium">中危</label>
                            
                            <input type="checkbox" id="filter-low" class="filter-checkbox" checked data-severity="low">
                            <label for="filter-low" class="filter-label filter-label-low">低危</label>
                            
                            <input type="checkbox" id="filter-info" class="filter-checkbox" checked data-severity="info">
                            <label for="filter-info" class="filter-label filter-label-info">信息</label>
                        </div>
                    </div>
                    
                    <div class="filter-group">
                        <h4>漏洞类型</h4>
                        <div id="vulnTypeFilters" class="filter-options">
                            <!-- 将由JavaScript根据实际漏洞类型动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="main-content">
                <!-- 概览部分 -->
                <section id="overview" class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-home"></i> 项目概览</h2>
                    </div>
                    <div class="card-body">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="icon"><i class="fas fa-file-code"></i></div>
                    <div class="count">{{ scan_info.scanned_files }}</div>
                                <div class="label">扫描文件数</div>
                </div>
                            
                            <div class="stat-card">
                                <div class="icon"><i class="fas fa-code"></i></div>
                    <div class="count">{{ scan_info.scanned_lines }}</div>
                                <div class="label">扫描代码行数</div>
                </div>
                            
                            <div class="stat-card">
                                <div class="icon"><i class="fas fa-clock"></i></div>
                    <div class="count">{{ scan_info.scan_duration }}</div>
                                <div class="label">扫描时长</div>
                </div>
                            
                            <div class="stat-card">
                                <div class="icon"><i class="fas fa-bug"></i></div>
                    <div class="count">{{ total_vulnerabilities }}</div>
                                <div class="label">漏洞总数</div>
                </div>
            </div>
            
                        <div class="stats-grid">
                            <div class="stat-card severity-critical">
                                <div class="icon"><i class="fas fa-skull-crossbones"></i></div>
                                <div class="count">{{ vulnerabilities_by_severity.critical|length }}</div>
                                <div class="label">严重漏洞</div>
                </div>
                            
                            <div class="stat-card severity-high">
                                <div class="icon"><i class="fas fa-radiation"></i></div>
                                <div class="count">{{ vulnerabilities_by_severity.high|length }}</div>
                                <div class="label">高危漏洞</div>
                </div>
                            
                            <div class="stat-card severity-medium">
                                <div class="icon"><i class="fas fa-exclamation-triangle"></i></div>
                                <div class="count">{{ vulnerabilities_by_severity.medium|length }}</div>
                                <div class="label">中危漏洞</div>
                </div>
                            
                            <div class="stat-card severity-low">
                                <div class="icon"><i class="fas fa-info-circle"></i></div>
                                <div class="count">{{ vulnerabilities_by_severity.low|length }}</div>
                                <div class="label">低危漏洞</div>
                </div>
                </div>
        
                        <!-- 图表区域 -->
            <div class="charts-section">
                <div class="chart-container">
                                <canvas id="severity-chart"></canvas>
                </div>
                <div class="chart-container">
                                <canvas id="vulnerability-chart"></canvas>
                </div>
                </div>
            </div>
        </section>
        
                <!-- 漏洞详情部分 -->
                <section id="vulnerabilities" class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-bug"></i> 漏洞详情</h2>
                        <div class="actions">
                            <button id="expandAll" class="btn"><i class="fas fa-expand-alt"></i> 展开全部</button>
                            <button id="collapseAll" class="btn"><i class="fas fa-compress-alt"></i> 折叠全部</button>
            </div>
                    </div>
                    <div class="card-body">
                        <div id="vulnerabilities-container">
                            <!-- 严重漏洞 -->
            {% if 'critical' in vulnerabilities_by_severity and vulnerabilities_by_severity.critical|length > 0 %}
                            <div class="severity-section" data-severity="critical">
                                <h3><i class="fas fa-skull-crossbones"></i> 严重漏洞 <span class="count-badge">{{ vulnerabilities_by_severity.critical|length }}</span></h3>
            {% for vuln in vulnerabilities_by_severity.critical %}
                                <div class="vuln-card" data-severity="critical" data-type="{{ vuln.vulnerability_type }}">
                <div class="card-header">
                    <span>{{ vuln.vulnerability_type }}</span>
                    <span class="severity-badge badge-critical">严重</span>
                </div>
                <div class="card-body">
                    <p><strong>漏洞ID:</strong> {{ vuln.id }}</p>
                    <p><strong>文件位置:</strong> <span class="file-path">{{ vuln.file_path }}</span></p>
                    <p><strong>行范围:</strong> {{ vuln.start_line }}-{{ vuln.end_line }}</p>
                    <p><strong>描述:</strong> {{ vuln.description }}</p>
                    
                    {% if vuln.snippet %}
                                        <div class="code-header">
                                            <span class="code-path">{{ vuln.file_path }}:{{ vuln.start_line }}</span>
                                            <button class="code-copy" data-clipboard-target="#snippet-{{ vuln.id }}"><i class="fas fa-copy"></i></button>
                                        </div>
                    <div class="code-section markdown-display">
                                            <pre id="snippet-{{ vuln.id }}">{{ vuln.snippet|e }}</pre>
                    </div>
                    {% endif %}
                </div>
                
                {% for remediation in remediations %}
                    {% if remediation.vulnerability_id == vuln.id %}
                    <div class="card-footer">
                                            <h4><i class="fas fa-tools"></i> 修复建议</h4>
                        <div class="remediation-section markdown-display">
                            {{ remediation.specific_remediation|e }}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endfor %}
                            </div>
            {% endif %}
            
                            <!-- 高危漏洞 -->
            {% if vulnerabilities_by_severity.high|length > 0 %}
                            <div class="severity-section" data-severity="high">
                                <h3><i class="fas fa-radiation"></i> 高危漏洞 <span class="count-badge">{{ vulnerabilities_by_severity.high|length }}</span></h3>
            {% for vuln in vulnerabilities_by_severity.high %}
                                <div class="vuln-card" data-severity="high" data-type="{{ vuln.vulnerability_type }}">
                <div class="card-header">
                    <span>{{ vuln.vulnerability_type }}</span>
                    <span class="severity-badge badge-high">高危</span>
                </div>
                <div class="card-body">
                    <p><strong>漏洞ID:</strong> {{ vuln.id }}</p>
                    <p><strong>文件位置:</strong> <span class="file-path">{{ vuln.file_path }}</span></p>
                    <p><strong>行范围:</strong> {{ vuln.start_line }}-{{ vuln.end_line }}</p>
                    <p><strong>描述:</strong> {{ vuln.description }}</p>
                    
                    {% if vuln.snippet %}
                                        <div class="code-header">
                                            <span class="code-path">{{ vuln.file_path }}:{{ vuln.start_line }}</span>
                                            <button class="code-copy" data-clipboard-target="#snippet-{{ vuln.id }}"><i class="fas fa-copy"></i></button>
                                        </div>
                    <div class="code-section markdown-display">
                                            <pre id="snippet-{{ vuln.id }}">{{ vuln.snippet|e }}</pre>
                    </div>
                    {% endif %}
                </div>
                
                {% for remediation in remediations %}
                    {% if remediation.vulnerability_id == vuln.id %}
                    <div class="card-footer">
                                            <h4><i class="fas fa-tools"></i> 修复建议</h4>
                        <div class="remediation-section markdown-display">
                            {{ remediation.specific_remediation|e }}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endfor %}
                            </div>
            {% endif %}
            
                            <!-- 中危漏洞 -->
            {% if vulnerabilities_by_severity.medium|length > 0 %}
                            <div class="severity-section" data-severity="medium">
                                <h3><i class="fas fa-exclamation-triangle"></i> 中危漏洞 <span class="count-badge">{{ vulnerabilities_by_severity.medium|length }}</span></h3>
            {% for vuln in vulnerabilities_by_severity.medium %}
                                <div class="vuln-card" data-severity="medium" data-type="{{ vuln.vulnerability_type }}">
                <div class="card-header">
                    <span>{{ vuln.vulnerability_type }}</span>
                    <span class="severity-badge badge-medium">中危</span>
                </div>
                <div class="card-body">
                    <p><strong>漏洞ID:</strong> {{ vuln.id }}</p>
                    <p><strong>文件位置:</strong> <span class="file-path">{{ vuln.file_path }}</span></p>
                    <p><strong>行范围:</strong> {{ vuln.start_line }}-{{ vuln.end_line }}</p>
                    <p><strong>描述:</strong> {{ vuln.description }}</p>
                    
                    {% if vuln.snippet %}
                                        <div class="code-header">
                                            <span class="code-path">{{ vuln.file_path }}:{{ vuln.start_line }}</span>
                                            <button class="code-copy" data-clipboard-target="#snippet-{{ vuln.id }}"><i class="fas fa-copy"></i></button>
                                        </div>
                    <div class="code-section markdown-display">
                                            <pre id="snippet-{{ vuln.id }}">{{ vuln.snippet|e }}</pre>
                    </div>
                    {% endif %}
                </div>
                
                {% for remediation in remediations %}
                    {% if remediation.vulnerability_id == vuln.id %}
                    <div class="card-footer">
                                            <h4><i class="fas fa-tools"></i> 修复建议</h4>
                        <div class="remediation-section markdown-display">
                            {{ remediation.specific_remediation|e }}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endfor %}
                            </div>
            {% endif %}
            
                            <!-- 低危漏洞 -->
            {% if vulnerabilities_by_severity.low|length > 0 %}
                            <div class="severity-section" data-severity="low">
                                <h3><i class="fas fa-info-circle"></i> 低危漏洞 <span class="count-badge">{{ vulnerabilities_by_severity.low|length }}</span></h3>
            {% for vuln in vulnerabilities_by_severity.low %}
                                <div class="vuln-card" data-severity="low" data-type="{{ vuln.vulnerability_type }}">
                <div class="card-header">
                    <span>{{ vuln.vulnerability_type }}</span>
                    <span class="severity-badge badge-low">低危</span>
                </div>
                <div class="card-body">
                    <p><strong>漏洞ID:</strong> {{ vuln.id }}</p>
                    <p><strong>文件位置:</strong> <span class="file-path">{{ vuln.file_path }}</span></p>
                    <p><strong>行范围:</strong> {{ vuln.start_line }}-{{ vuln.end_line }}</p>
                    <p><strong>描述:</strong> {{ vuln.description }}</p>
                    
                    {% if vuln.snippet %}
                                        <div class="code-header">
                                            <span class="code-path">{{ vuln.file_path }}:{{ vuln.start_line }}</span>
                                            <button class="code-copy" data-clipboard-target="#snippet-{{ vuln.id }}"><i class="fas fa-copy"></i></button>
                                        </div>
                    <div class="code-section markdown-display">
                                            <pre id="snippet-{{ vuln.id }}">{{ vuln.snippet|e }}</pre>
                    </div>
                    {% endif %}
                </div>
                
                {% for remediation in remediations %}
                    {% if remediation.vulnerability_id == vuln.id %}
                    <div class="card-footer">
                                            <h4><i class="fas fa-tools"></i> 修复建议</h4>
                        <div class="remediation-section markdown-display">
                            {{ remediation.specific_remediation|e }}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endfor %}
                            </div>
            {% endif %}
            
                            <!-- 信息提醒 -->
            {% if vulnerabilities_by_severity.info|length > 0 %}
                            <div class="severity-section" data-severity="info">
                                <h3><i class="fas fa-info"></i> 信息提醒 <span class="count-badge">{{ vulnerabilities_by_severity.info|length }}</span></h3>
            {% for vuln in vulnerabilities_by_severity.info %}
                                <div class="vuln-card" data-severity="info" data-type="{{ vuln.vulnerability_type }}">
                <div class="card-header">
                    <span>{{ vuln.vulnerability_type }}</span>
                    <span class="severity-badge badge-info">信息</span>
                </div>
                <div class="card-body">
                    <p><strong>漏洞ID:</strong> {{ vuln.id }}</p>
                    <p><strong>文件位置:</strong> <span class="file-path">{{ vuln.file_path }}</span></p>
                    <p><strong>行范围:</strong> {{ vuln.start_line }}-{{ vuln.end_line }}</p>
                    <p><strong>描述:</strong> {{ vuln.description }}</p>
                    
                    {% if vuln.snippet %}
                                        <div class="code-header">
                                            <span class="code-path">{{ vuln.file_path }}:{{ vuln.start_line }}</span>
                                            <button class="code-copy" data-clipboard-target="#snippet-{{ vuln.id }}"><i class="fas fa-copy"></i></button>
                                        </div>
                    <div class="code-section markdown-display">
                                            <pre id="snippet-{{ vuln.id }}">{{ vuln.snippet|e }}</pre>
                    </div>
                    {% endif %}
                </div>
                
                {% for remediation in remediations %}
                    {% if remediation.vulnerability_id == vuln.id %}
                    <div class="card-footer">
                                            <h4><i class="fas fa-tools"></i> 修复建议</h4>
                        <div class="remediation-section markdown-display">
                            {{ remediation.specific_remediation|e }}
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
            </div>
            {% endfor %}
                            </div>
            {% endif %}
                        </div>
                    </div>
        </section>
        
                <!-- 依赖关系部分 -->
                {% if dependency_data %}
                <section id="dependency" class="card">
                <div class="card-header">
                        <h2><i class="fas fa-project-diagram"></i> 代码依赖关系</h2>
                </div>
                <div class="card-body">
                        <div id="dependency-chart" style="width: 100%; height: 500px;"></div>
                        <p class="chart-note">
                            <i class="fas fa-info-circle"></i> 此图显示了项目中各组件之间的依赖关系，可用于理解代码结构和识别潜在安全风险点。
                        </p>
                </div>
                </section>
                {% endif %}
                
                <!-- 安全建议部分 -->
                <section id="recommendations" class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-lightbulb"></i> 安全建议</h2>
                    </div>
                    <div class="card-body">
                        <div class="card">
                            <div class="card-header">
                                <h3>安全最佳实践</h3>
                            </div>
                            <div class="card-body">
                                <div class="recommendations-grid">
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-filter"></i></div>
                                        <h4>输入验证</h4>
                                        <p>验证所有来自外部来源的数据，确保它们符合预期格式和长度，防止注入类攻击。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-code"></i></div>
                                        <h4>输出编码</h4>
                                        <p>对所有输出到用户界面的数据进行适当的编码，防止XSS等攻击。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-database"></i></div>
                                        <h4>参数化查询</h4>
                                        <p>使用参数化查询和预处理语句，防止SQL注入攻击。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-user-shield"></i></div>
                                        <h4>身份验证和授权</h4>
                                        <p>实施强大的身份验证机制，并确保适当的授权检查。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-key"></i></div>
                                        <h4>安全密码存储</h4>
                                        <p>使用强哈希算法(如bcrypt)存储密码，并使用盐值增加安全性。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-lock"></i></div>
                                        <h4>敏感数据保护</h4>
                                        <p>对敏感数据进行加密存储和传输，使用适当的加密算法和密钥管理。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-cogs"></i></div>
                                        <h4>安全配置</h4>
                                        <p>确保应用程序和服务器配置遵循安全最佳实践，移除默认账户和不必要服务。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-exclamation-circle"></i></div>
                                        <h4>错误处理</h4>
                                        <p>实现安全的错误处理，避免泄露敏感信息，记录错误但对用户显示通用信息。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-clipboard-list"></i></div>
                                        <h4>日志记录和监控</h4>
                                        <p>记录安全相关事件并定期审查日志，实施监控以便及时发现异常活动。</p>
                                    </div>
                                    
                                    <div class="recommendation-item">
                                        <div class="icon"><i class="fas fa-boxes"></i></div>
                                        <h4>依赖管理</h4>
                                        <p>定期更新和审计第三方依赖，修复已知漏洞，使用软件成分分析工具。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 项目特定建议 -->
                        {% if remediations|length > 0 %}
                        <div class="card" style="margin-top: 20px;">
                            <div class="card-header">
                                <h3>项目特定建议</h3>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    {% for remediation in remediations %}
                                    <div class="timeline-item">
                                        <div class="timeline-marker"></div>
                                        <div class="timeline-content">
                                            <h4>{{ remediation.title }}</h4>
                                            <p>{{ remediation.description }}</p>
                                            {% if remediation.code_example %}
                                            <div class="code-header">
                                                <span>修复示例</span>
                                                <button class="code-copy" data-clipboard-target="#remediation-{{ loop.index }}"><i class="fas fa-copy"></i></button>
                                            </div>
                                            <div class="code-section markdown-display">
                                                <pre id="remediation-{{ loop.index }}">{{ remediation.code_example|e }}</pre>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
            </div>
        </section>
                
                <!-- 统计数据部分 -->
                <section id="statistics" class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-chart-pie"></i> 统计数据</h2>
                    </div>
                    <div class="card-body">
                        <div class="charts-section">
                            <div class="chart-container">
                                <h3>漏洞严重程度分布</h3>
                                <canvas id="severity-dist-chart"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <h3>最常见漏洞类型</h3>
                                <canvas id="vulnerability-types-chart"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <h3>文件复杂度分布</h3>
                                <canvas id="complexity-chart"></canvas>
                            </div>
                            
                            <div class="chart-container">
                                <h3>每日发现漏洞数量</h3>
                                <canvas id="daily-vulns-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        
        <footer>
            <p>由 <strong>AuditLuma</strong> 安全审计系统生成 | {{ scan_date }}</p>
            <p><small>版本 1.0.0 | <a href="https://github.com/Vistaminc/auditluma" target="_blank">GitHub</a></small></p>
        </footer>
    </div>
    
    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.10/dist/clipboard.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/java.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/sql.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/languages/php.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化剪贴板
            new ClipboardJS('.code-copy').on('success', function(e) {
                const button = e.trigger;
                const icon = button.querySelector('i');
                
                icon.classList.remove('fa-copy');
                icon.classList.add('fa-check');
                
                setTimeout(() => {
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-copy');
                }, 2000);
            });
            
            // 导航菜单激活状态
            const navLinks = document.querySelectorAll('.nav-menu a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 添加点击事件到漏洞卡片标题
            document.querySelectorAll('.vuln-card .card-header').forEach(header => {
                header.addEventListener('click', function() {
                    const card = this.closest('.vuln-card');
                    card.classList.toggle('expanded');
                });
            });
            
            // 默认展开所有卡片
            document.querySelectorAll('.vuln-card').forEach(card => {
                card.classList.add('expanded');
            });
            
            // 添加漏洞类型过滤器
            const vulnTypes = new Set();
            document.querySelectorAll('.vuln-card').forEach(card => {
                vulnTypes.add(card.getAttribute('data-type'));
            });
            
            const vulnTypeFilters = document.getElementById('vulnTypeFilters');
            Array.from(vulnTypes).sort().forEach((type, index) => {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `filter-type-${index}`;
                checkbox.className = 'filter-checkbox';
                checkbox.checked = true;
                checkbox.setAttribute('data-type', type);
                
                const label = document.createElement('label');
                label.htmlFor = `filter-type-${index}`;
                label.className = 'filter-label';
                label.textContent = type;
                
                vulnTypeFilters.appendChild(checkbox);
                vulnTypeFilters.appendChild(label);
            });
            
            // 过滤功能
            const filterVulnerabilities = () => {
                const severityFilters = Array.from(document.querySelectorAll('.filter-checkbox[data-severity]:checked')).map(el => el.getAttribute('data-severity'));
                const typeFilters = Array.from(document.querySelectorAll('.filter-checkbox[data-type]:checked')).map(el => el.getAttribute('data-type'));
                const searchTerm = document.getElementById('searchInput').value.toLowerCase();
                
                document.querySelectorAll('.vuln-card').forEach(card => {
                    const severity = card.getAttribute('data-severity');
                    const type = card.getAttribute('data-type');
                    const text = card.textContent.toLowerCase();
                    
                    const severityMatch = severityFilters.includes(severity);
                    const typeMatch = typeFilters.includes(type);
                    const searchMatch = searchTerm === '' || text.includes(searchTerm);
                    
                    card.style.display = (severityMatch && typeMatch && searchMatch) ? 'block' : 'none';
                });
                
                // 更新各严重程度部分可见性
                document.querySelectorAll('.severity-section').forEach(section => {
                    const sectionSeverity = section.getAttribute('data-severity');
                    const hasVisibleCards = Array.from(section.querySelectorAll('.vuln-card')).some(card => card.style.display !== 'none');
                    section.style.display = hasVisibleCards ? 'block' : 'none';
                });
            };
            
            // 添加过滤器事件监听
            document.querySelectorAll('.filter-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', filterVulnerabilities);
            });
            
            document.getElementById('searchInput').addEventListener('input', filterVulnerabilities);
            
            // 展开/折叠按钮
            document.getElementById('expandAll').addEventListener('click', function() {
                document.querySelectorAll('.vuln-card').forEach(card => {
                    card.classList.add('expanded');
                });
            });
            
            document.getElementById('collapseAll').addEventListener('click', function() {
                document.querySelectorAll('.vuln-card').forEach(card => {
                    card.classList.remove('expanded');
                });
            });
            
            // 漏洞类型分布图
            const vulnerabilityCtx = document.getElementById('vulnerability-chart').getContext('2d');
            const vulnTypeLabels = {{ vuln_types|safe }};
            const vulnTypeCounts = {{ vuln_type_counts|safe }};
            
            new Chart(vulnerabilityCtx, {
                type: 'doughnut',
                data: {
                    labels: vulnTypeLabels,
                    datasets: [{
                        data: vulnTypeCounts,
                        backgroundColor: [
                            '#00c0f5', // 网安蓝
                            '#60b5f0', // 浅蓝
                            '#50a6e6', // 蓝色
                            '#7ad84e', // 绿色
                            '#ffbd59', // 黄色
                            '#ff9e58', // 橙色
                            '#ff6b6b', // 网安红
                            '#d456ff', // 紫色
                            '#59c0ff', // 湖蓝
                            '#c5c3c6'  // 浅灰
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                padding: 20,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: '漏洞类型分布',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });

            // 漏洞严重程度分布图
            const severityCtx = document.getElementById('severity-dist-chart').getContext('2d');
            const severityCounts = [
                {{ stats.high_count }}, 
                {{ stats.medium_count }}, 
                {{ stats.low_count }}
            ];
            
            new Chart(severityCtx, {
                type: 'pie',
                data: {
                    labels: ['高危', '中危', '低危'],
                    datasets: [{
                        data: severityCounts,
                        backgroundColor: ['#ff6b6b', '#ffbd59', '#7ad84e'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 最常见漏洞类型图
            const vulnTypesCtx = document.getElementById('vulnerability-types-chart').getContext('2d');
            const commonVulnLabels = {{ common_vuln_types|safe }};
            const commonVulnCounts = {{ common_vuln_counts|safe }};
            
            new Chart(vulnTypesCtx, {
                type: 'bar',
                data: {
                    labels: commonVulnLabels,
                    datasets: [{
                        label: '漏洞数量',
                        data: commonVulnCounts,
                        backgroundColor: '#00c0f5',
                        borderRadius: 8
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                drawBorder: false
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 文件复杂度分布图
            const complexityCtx = document.getElementById('complexity-chart').getContext('2d');
            const complexityLabels = {{ complexity_labels|safe }};
            const complexityCounts = {{ complexity_counts|safe }};
            
            new Chart(complexityCtx, {
                type: 'bar',
                data: {
                    labels: complexityLabels,
                    datasets: [{
                        label: '文件数量',
                        data: complexityCounts,
                        backgroundColor: '#00c0f5',
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                drawBorder: false
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 每日发现漏洞数量
            const dailyVulnsCtx = document.getElementById('daily-vulns-chart').getContext('2d');
            const dailyLabels = {{ daily_labels|safe }};
            
            // 准备每日漏洞数据
            const highData = [];
            const mediumData = [];
            const lowData = [];
            
            {% for day in daily_data %}
                highData.push({{ day.high }});
                mediumData.push({{ day.medium }});
                lowData.push({{ day.low }});
            {% endfor %}
            
            new Chart(dailyVulnsCtx, {
                type: 'line',
                data: {
                    labels: dailyLabels,
                    datasets: [
                        {
                            label: '高危',
                            data: highData,
                            borderColor: '#ff6b6b',
                            backgroundColor: 'rgba(255, 107, 107, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '中危',
                            data: mediumData,
                            borderColor: '#ffbd59',
                            backgroundColor: 'rgba(255, 189, 89, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '低危',
                            data: lowData,
                            borderColor: '#7ad84e',
                            backgroundColor: 'rgba(122, 216, 78, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            stacked: false
                        }
                    }
                }
            });

            // 依赖关系图
            {% if dependency_data %}
            const dependencyChart = echarts.init(document.getElementById('dependency-chart'));
            const dependencyOption = {{ dependency_data|safe }};
            dependencyChart.setOption(dependencyOption);
            
            window.addEventListener('resize', function() {
                dependencyChart.resize();
            });
            {% endif %}

            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        window.scrollTo({
                            top: target.offsetTop - 20,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // 代码高亮和Markdown处理
            function processMarkdownCode() {
                // 处理所有代码区域
                document.querySelectorAll('.code-section pre').forEach(function(pre) {
                    // 检查是否是Markdown格式的代码块
                    const content = pre.textContent;
                    if (content.trim().startsWith('```')) {
                        // 提取语言和代码内容
                        const match = content.match(/```(\w*)\n([\s\S]*?)```/);
                        if (match) {
                            const language = match[1] || 'plaintext';
                            const code = match[2];
                            
                            // 创建新的代码元素
                            const codeElement = document.createElement('code');
                            codeElement.className = `language-${language}`;
                            codeElement.textContent = code;
                            
                            // 替换原内容
                            pre.innerHTML = '';
                            pre.appendChild(codeElement);
                            
                            // 应用高亮
                            hljs.highlightElement(codeElement);
                        }
                    } else {
                        // 普通代码块，直接应用高亮
                        const codeElement = document.createElement('code');
                        codeElement.textContent = content;
                        
                        // 尝试检测语言
                        let language = 'plaintext';
                        if (content.includes('def ') || content.includes('import ')) {
                            language = 'python';
                        } else if (content.includes('function') || content.includes('var ')) {
                            language = 'javascript';
                        } else if (content.includes('<html') || content.includes('</div>')) {
                            language = 'xml';
                        } else if (content.includes('@media') || content.includes('{')) {
                            language = 'css';
                        }
                        
                        codeElement.className = `language-${language}`;
                        
                        // 替换原内容
                        pre.innerHTML = '';
                        pre.appendChild(codeElement);
                        
                        // 应用高亮
                        hljs.highlightElement(codeElement);
                    }
                });
                
                // 处理所有修复建议
                document.querySelectorAll('.remediation-section').forEach(function(section) {
                    const content = section.textContent;
                    
                    // 将内容解析为Markdown
                    let html = content;
                    
                    // 1. 处理代码块
                    html = html.replace(/```(\w*)\n([\s\S]*?)```/g, function(match, language, code) {
                        language = language || 'plaintext';
                        return `<pre><code class="language-${language}">${escapeHtml(code)}</code></pre>`;
                    });
                    
                    // 2. 处理内联代码
                    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
                    
                    // 3. 处理标题
                    html = html.replace(/^### (.*?)$/gm, '<h3>$1</h3>');
                    html = html.replace(/^## (.*?)$/gm, '<h2>$1</h2>');
                    html = html.replace(/^# (.*?)$/gm, '<h1>$1</h1>');
                    
                    // 4. 处理列表
                    html = html.replace(/^\d+\. (.*?)$/gm, '<li>$1</li>');
                    html = html.replace(/^- (.*?)$/gm, '<li>$1</li>');
                    html = html.replace(/(<li>.*?<\/li>\n)+/g, '<ul>$&</ul>');
                    
                    // 5. 处理段落
                    html = html.replace(/^(?!<[hou]).+$/gm, '<p>$&</p>');
                    
                    // 确保内容被包装在markdown-content中
                    section.innerHTML = `<div class="markdown-content">${html}</div>`;
                    
                    // 对新内容应用代码高亮
                    section.querySelectorAll('pre code').forEach(block => {
                        hljs.highlightElement(block);
                    });
                });
            }
            
            // HTML转义
            function escapeHtml(text) {
                const map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#039;'
                };
                return text.replace(/[&<>"']/g, m => map[m]);
            }
            
            // 应用代码高亮和Markdown处理
            processMarkdownCode();
        });
    </script>
</body>
</html>
