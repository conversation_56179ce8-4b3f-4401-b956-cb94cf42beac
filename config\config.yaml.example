# AuditLuma 配置文件

# 全局设置
global:
  # 是否显示代理思考过程 
  show_thinking: false
  # 默认语言
  language: "zh-CN"
  # 目标项目目录
  target_dir: "./goalfile"
  # 报告输出目录
  report_dir: "./reports"
  # 报告格式
  report_format: "html"

# OpenAI 配置
openai:
  model: "gpt-4-turbo-preview"
  api_key: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  # 如果使用反向代理或中转，可以设置 base_url
  base_url: "https://api.lightai.io/v1"
  # 模型参数设置
  max_tokens: 8000
  temperature: 0.1

# DeepSeek 配置
deepseek:
  model: "deepseek-chat"
  api_key: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
  base_url: "https://api.deepseek.com/v1"
  max_tokens: 8000
  temperature: 0.1

# 硅基流动配置
moonshot:
  model: "moonshot-v1-8k"
  api_key: ""
  base_url: "https://api.moonshot.cn/v1"
  max_tokens: 8000
  temperature: 0.1

# 通义千问配置
qwen:
  model: "qwen-max"
  api_key: ""
  base_url: "https://dashscope.aliyuncs.com/api/v1"
  max_tokens: 8000
  temperature: 0.1

# 智谱AI配置
zhipu:
  model: "glm-4"
  api_key: ""
  base_url: "https://open.bigmodel.cn/api/paas/v4"
  max_tokens: 8000
  temperature: 0.1

# 百度千帆配置
baichuan:
  model: "Baichuan4"
  api_key: ""
  base_url: "https://api.baichuan-ai.com/v1"
  max_tokens: 8000
  temperature: 0.1

# Ollama 配置
ollama:
  model: "llama2"  # 或其他您部署的模型名称，如 "qwen"、"deepseek" 等
  api_key: ""  # Ollama 本地部署不需要 API key
  base_url: "http://localhost:11434/v1"  # Ollama 的 API 地址
  max_tokens: 8000
  temperature: 0.1
# Ollama_emd 配置
ollama_emd:
  model: "mxbai-embed-large:latest"  # 或其他您部署的模型名称，如 "qwen"、"deepseek" 等
  api_key: ""  # Ollama 本地部署不需要 API key
  base_url: "http://localhost:11434/v1/embeddings"  # Ollama 的 API 地址
  max_tokens: 8000
  temperature: 0.1

# 代理设置
agent:
  # 默认使用的LLM提供商
  default_provider: "openai"
  # 系统提示词
  system_prompt: "你是一个专业的代码安全审计助手，将帮助用户分析代码中的安全漏洞"
  # 记忆容量
  memory_limit: 10

# 项目配置
project:
  name: "AuditLuma项目"
  max_file_size: 1000000  # 处理的最大文件大小（字节）
  max_batch_size: 20  # 并行处理的最大文件数
  ignored_extensions: [".jpg", ".png", ".gif", ".mp3", ".mp4", ".zip", ".tar", ".gz"]
  ignored_directories: ["node_modules", "__pycache__", ".git", "dist", "build", "venv", "env"]

# Self-RAG 配置
self_rag:
  enabled: true
  vector_store: "faiss"
  embedding_model: "mxbai-embed-large:latest@ollama_emd"  # 使用OpenAI的嵌入模型构建知识库，仅用于生成嵌入
  chunk_size: 1000
  chunk_overlap: 200
  max_documents: 10000
  retrieval_k: 5
  relevance_threshold: 0.75

# 工具设置
tools:
  # 启用的工具列表
  enabled: ["code_analyzer", "security_scanner", "dependency_analyzer"]

# 多智能体协作协议 (MCP)
mcp:
  enabled: true
  agents:
    - name: "orchestrator"
      description: "协调所有智能体和工作流程"
      type: "coordinator"
      priority: 1
    - name: "code_parser"
      description: "分析代码结构并提取依赖关系"
      type: "analyzer"
      priority: 2
    - name: "security_analyst"
      description: "识别安全漏洞"
      type: "analyst"
      priority: 3
    - name: "code_analyzer"
      description: "分析代码结构和依赖关系"
      type: "analyzer" 
      priority: 4
    - name: "remediation"
      description: "提供代码修复建议和最佳实践"
      type: "generator"
      priority: 5

# UI设置
ui:
  # 主题颜色
  theme: "blue"
  # 是否在终端中使用彩色输出
  use_colors: true
  # 详细程度
  verbosity: "normal"

# 漏洞数据库
vulnerability_db:
  sources:
    - "OWASP Top 10"
    - "CWE Top 25"
    - "SANS Top 25"
  update_frequency: "weekly"
  local_storage: "./data/vulnerability_db"

# 输出配置
output:
  formats: ["html", "json", "markdown"]
  visualization: true
  graph_format: "d3"
  max_results: 100
  severity_levels: ["critical", "high", "medium", "low", "info"]

# 默认模型配置
default_models:
  code_analysis: "deepseek-r1:1.5b@ollama"  
  security_audit: "deepseek-r1:1.5b@ollama"
  remediation: "deepseek-r1:1.5b@ollama"
  summarization: "deepseek-r1:1.5b@ollama"
