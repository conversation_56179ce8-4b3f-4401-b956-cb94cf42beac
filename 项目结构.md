# AuditLuma 项目结构

## 项目概述
AuditLuma 是一个高级代码审计AI系统，用于自动化代码安全审计和漏洞检测。

## 完整目录结构

```
.
├── app/                            # 应用代码
│   ├── api/                       # API 接口
│   ├── desktop/                   # 桌面应用代码
│   └── web/                       # Web 应用代码
│
├── auditluma/                    # 核心 Python 包
│   ├── agents/                    # AI 代理实现
│   │   ├── __init__.py
│   │   ├── base.py                # 代理基类
│   │   ├── code_analyzer.py       # 代码分析代理
│   │   ├── orchestrator.py        # 代理编排器
│   │   ├── remediation.py         # 修复建议代理
│   │   └── security_analyst.py    # 安全分析代理
│   │
│   ├── mcp/                     # 模型控制平面
│   │   ├── __init__.py
│   │   └── protocol.py            # 通信协议
│   │
│   ├── mocks/                   # 测试模拟对象
│   │   ├── __init__.py
│   │   └── llm_client.py          # LLM 客户端模拟
│   │
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   └── code.py                # 代码相关模型
│   │
│   ├── parsers/                 # 代码解析器
│   │   ├── __init__.py
│   │   └── code_parser.py         # 代码解析器实现
│   │
│   ├── rag/                     # 检索增强生成
│   │   ├── __init__.py
│   │   └── self_rag.py            # 自检索增强生成实现
│   │
│   ├── templates/               # 报告模板
│   │   └── report.html           # HTML 报告模板
│   │
│   ├── visualizer/              # 可视化组件
│   │   ├── __init__.py
│   │   ├── graph_visualizer.py    # 图表可视化
│   │   ├── report_generator.py    # 报告生成器
│   │   └── templates/             # 可视化模板
│   │       └── report_template.html
│   │
│   ├── __init__.py
│   ├── config.py                  # 配置管理
│   ├── orchestrator.py            # 主编排器
│   ├── scanner.py                 # 代码扫描器
│   └── utils.py                   # 工具函数
│
├── config/                      # 配置文件
│   └── config.yaml.example        # 配置示例
│
├── lib/                         # 第三方库
│   ├── bindings/                 # 语言绑定
│   │   └── utils.js
│   ├── tom-select/               # 选择框组件
│   └── vis-9.1.2/                # 可视化库
│
├── tests/                       # 测试代码
│   └── ...
│
├── .gitignore                   # Git 忽略文件
├── .python-version               # Python 版本
├── .windsurfrules               # Windsurf 规则
├── example_test_code_examin.py   # 示例测试代码
├── glconfig.py                  # 全局配置
├── LICENSE                      # 许可证
├── main.py                      # 主程序入口
├── README.md                    # 项目说明
└── 项目结构.md                  # 本文件
```

## 主要组件说

### 核心功能模块
- `main.py`: 程序主入口，负责参数解析和任务调度
- `glconfig.py`: 全局配置文件
- `auditluma/`: 核心功能实现
  - `agents/`: AI代理实现，负责代码分析和漏洞检测
  - `models/`: 数据模型定义
  - `parsers/`: 代码解析器，支持多种编程语言
  - `visualizer/`: 报告生成和可视化组件

### 应用接口
- `app/api/`: RESTful API 接口
- `app/desktop/`: 桌面应用代码
- `app/web/`: Web 应用前端代码

### 配置和资源
- `config/`: 配置文件目录
  - `config.yaml`: 主配置文件
- `lib/`: 第三方依赖库

## 开发指南

### 环境要求
- Python 3.8+
- 依赖管理: `requirements.txt`

### 快速开始

1. 安装依赖:
   ```bash
   pip install -r requirements.txt
   ```

2. 运行主程序:
   ```bash
   python main.py -d /path/to/target/code -o ./reports
   ```

## 贡献指南

1. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
2. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
3. 推送到分支 (`git push origin feature/AmazingFeature`)
4. 创建 Pull Request

## 许可证
[MIT]
